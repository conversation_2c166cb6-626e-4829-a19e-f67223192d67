/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      data_manager.dart
///
/// DESCRIPTION :    数据管理服务，负责应用数据的统一管理、WebSocket事件处理、
///                  服务器列表维护和用户信息管理，提供完整的数据层抽象
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../models/user_info.dart';
import '../models/server.dart';
import '../models/traffic_stats.dart';
import '../models/interface_info.dart';
import '../models/routing_settings.dart';
import '../core/app_state.dart';
import '../core/error_handler.dart';
import '../core/dependency_injection.dart';
import '../utils/api_exception.dart';
import '../utils/constants.dart';
import '../utils/async_operation_manager.dart';
import 'api_service.dart';

import 'websocket_service.dart';
import 'log_service.dart';
import 'routing_settings_service.dart';
import 'platform/platform_service_factory.dart';
import 'platform/cross_platform_storage_service.dart';

/// DataManager
///
/// PURPOSE:
///     数据管理服务，负责应用数据的统一管理、WebSocket事件处理和数据持久化
///
/// FEATURES:
///     - WebSocket事件处理：监听和处理实时数据更新事件
///     - 服务器管理：服务器列表的获取、缓存和智能更新
///     - 用户信息管理：用户数据的加载、保存和清理
///     - 流量统计：实时流量数据的接收和状态更新
///     - 接口信息：网络接口状态的监控和更新
///     - 数据持久化：本地数据存储和异步操作管理
///     - 状态同步：与AppState的数据同步和通知
///     - Clean Architecture：遵循清晰架构原则，服务层与UI层分离
///
/// USAGE:
///     通过依赖注入获取实例，监听数据变化，调用数据管理方法
class DataManager extends ChangeNotifier {
  Timer? _dataRefreshTimer;
  List<Server> _servers = [];

  // 辅助类实例
  late final _DataManagerHelper _helper;

  // 跨平台存储服务实例
  late final CrossPlatformStorageService _storageService;

  /// DataManager构造函数
  ///
  /// DESCRIPTION:
  ///     创建数据管理器实例并延迟初始化事件处理器
  ///
  /// PARAMETERS:
  ///     无
  DataManager() {
    // 初始化辅助类
    _helper = _DataManagerHelper();

    // 初始化跨平台存储服务
    _storageService = PlatformServiceFactory.createStorageService();

    // 延迟初始化事件处理器，避免循环依赖
    Future.microtask(() => _initializeEventHandlers());
  }

  /// apiService getter
  ///
  /// DESCRIPTION:
  ///     获取API服务实例
  ///
  /// RETURNS:
  ///     ApiService - API服务实例
  ApiService get apiService => _helper.getApiService();

  /// webSocketService getter
  ///
  /// DESCRIPTION:
  ///     获取WebSocket服务实例
  ///
  /// RETURNS:
  ///     WebSocketService - WebSocket服务实例
  WebSocketService get webSocketService => _helper.getWebSocketService();

  /// logService getter
  ///
  /// DESCRIPTION:
  ///     获取日志服务实例
  ///
  /// RETURNS:
  ///     LogService - 日志服务实例
  LogService get logService => _helper.getLogService();

  /// appState getter
  ///
  /// DESCRIPTION:
  ///     获取应用状态实例
  ///
  /// RETURNS:
  ///     AppState - 应用状态实例
  AppState get appState => _helper.getAppState();

  /// _initializeEventHandlers
  ///
  /// DESCRIPTION:
  ///     初始化WebSocket事件处理器，监听各种实时数据更新事件
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _initializeEventHandlers() {
    // 监听WebSocket事件流
    webSocketService.events.listen((event) {
      final eventType = event['event'] as String?;
      final eventData = event['data'];

      // Debug log for all WebSocket events
      // logService.info('DataManager', 'Received WebSocket event - type: $eventType, has_data: ${eventData != null}');

      switch (eventType) {
        case 'traffic':
          if (eventData != null && eventData is Map<String, dynamic>) {
            try {
              // logService.debug('DataManager', 'Received WebSocket traffic statistics event: $eventData');
              final trafficStats = TrafficStats.fromJson(eventData);
              // logService.debug('DataManager', 'WebSocket traffic statistics parsed successfully: upload=${trafficStats.uploadSpeed}B/s, download=${trafficStats.downloadSpeed}B/s, total_upload=${trafficStats.totalUpload}B, total_download=${trafficStats.totalDownload}B');
              appState.updateTrafficStats(trafficStats);
              // logService.debug('DataManager', 'AppState traffic statistics updated');
            } catch (e) {
              logService.error('DataManager', 'Failed to parse traffic statistics data: ${e.toString()}');
              // logService.debug('DataManager', 'Raw traffic statistics data: $eventData');
            }
          } else {
            logService.warning('DataManager', 'Received invalid traffic statistics data: ${eventData?.runtimeType}');
          }
          break;

        case 'interface_info':
          // logService.debug('DataManager', 'Received interface_info WebSocket event: $eventData');
          if (eventData != null && eventData is Map<String, dynamic>) {
            try {
              final interfaceInfo = InterfaceInfo.fromJson(eventData);
              logService.debug('DataManager', 'Parsed interface info from WebSocket - '
                  'interfaceName: ${interfaceInfo.interfaceName}, '
                  'localIp: ${interfaceInfo.localIp}, '
                  'tunIp: ${interfaceInfo.tunIp ?? "empty"}');

              // 确保WebSocket数据优先级高于API调用
              // updateInterfaceInfo方法内部会处理cloud ip为空时的保护逻辑
              appState.updateInterfaceInfo(interfaceInfo);

              logService.debug('DataManager', 'Successfully updated AppState with WebSocket interface info - '
                  'this should be the primary data source for interface information');
            } catch (e) {
              logService.error('DataManager', 'Failed to parse interface info from WebSocket: $e');
            }
          } else {
            logService.error('DataManager', 'Invalid interface_info WebSocket event data: ${eventData?.runtimeType}');
          }
          break;

        case 'status':
          if (eventData != null && eventData is Map<String, dynamic>) {
            try {
              final status = _parseConnectionStatus(eventData['status']);
              final message = eventData['message'] ?? 'Status updated'; // Fallback message for status events

              // Debug log to see all status event data
              // logService.info('DataManager', 'Received status event - status: $status, message: $message, latency_ms: ${eventData['latency_ms']}, has_server: ${eventData['server'] != null}');

              // logService.debug('DataManager', 'Received status event - status: $status, message: $message, raw_data: $eventData');

              // 处理连接时间
              DateTime? connectedTime;
              if (eventData['connected_time'] != null) {
                try {
                  final timestamp = eventData['connected_time'] as int;
                  // Only parse if timestamp is valid (greater than 0)
                  if (timestamp > 0) {
                    connectedTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
                    // logService.debug('DataManager', 'Parsed connection time: ${connectedTime.toString()}, status: $status');
                  } else {
                    // logService.debug('DataManager', 'Received invalid timestamp: $timestamp, ignoring');
                  }
                } catch (e) {
                  logService.error('DataManager', 'Failed to parse connection time', e);
                }
              }

              // 处理延迟信息（来自VPN连接认证）
              if (eventData['latency_ms'] != null) {
                try {
                  final latencyMs = eventData['latency_ms'] as int?;
                  // logService.debug('DataManager', 'Processing latency - latencyMs: $latencyMs, status: $status');

                  if (latencyMs != null && latencyMs > 0) {
                    final currentServer = appState.selectedServer;
                    // logService.debug('DataManager', 'Current server - id: ${currentServer?.id}, name: ${currentServer?.name}');

                    if (currentServer != null) {
                      logService.info('DataManager', 'Updating server latency from VPN authentication - '
                          'server: ${currentServer.name}, old_ping: ${currentServer.ping}ms, new_latency: ${latencyMs}ms');

                      // 更新当前服务器的延迟信息
                      final updatedServer = Server(
                        id: currentServer.id,
                        name: currentServer.name,
                        nameEn: currentServer.nameEn,
                        serverName: currentServer.serverName,
                        serverPort: currentServer.serverPort,
                        ping: latencyMs,
                        isAuto: currentServer.isAuto,
                        status: currentServer.status,
                        isDefault: currentServer.isDefault,
                      );

                      // 更新AppState中的选中服务器
                      appState.updateServerInfo(updatedServer);

                      // 同时更新DataManager中的服务器列表
                      _updateServerInList(updatedServer);
                    } else {
                      logService.warning('DataManager', 'Cannot update latency - no selected server');
                    }
                  } else {
                    // logService.debug('DataManager', 'Skipping latency update - invalid value: $latencyMs');
                  }
                } catch (e) {
                  logService.error('DataManager', 'Failed to parse latency info from status event', e);
                }
              }

              // 处理服务器信息
              if (eventData['server'] != null && eventData['server'] is Map<String, dynamic>) {
                try {
                  final server = Server.fromJson(eventData['server']);
                  // Use English name for logs
                  final serverLogName = server.nameEn.isNotEmpty ? server.nameEn : server.name;
                  logService.info('DataManager', 'Received server info from status event: $serverLogName (${server.serverName}:${server.serverPort})');

                  // 检查是否是有效的服务器信息（有ID和名称）
                  if (server.id.isNotEmpty && server.name.isNotEmpty) {
                    // 只在连接成功时或没有选中服务器时更新服务器信息
                    if (status == ConnectionStatus.connected || appState.selectedServer == null) {
                      logService.info('DataManager', 'Updating connected server info: $serverLogName (current selected: ${appState.selectedServer?.name ?? "null"})');
                      appState.selectServer(server, source: ServerSelectionSource.backendPush);
                    } else if (appState.selectedServer?.id == server.id) {
                      // 同一服务器的信息更新（如延迟更新）
                      appState.updateServerInfo(server);
                      // logService.debug('DataManager', 'Updated server info for current selection: $serverLogName');
                    } else {
                      // 不同服务器且用户已有选择，忽略后端推送
                      // final currentServerLogName = appState.selectedServer?.nameEn.isNotEmpty == true
                      //     ? appState.selectedServer!.nameEn
                      //     : appState.selectedServer?.name ?? 'None';
                      // logService.debug('DataManager', 'Ignoring backend server push: keeping user selected $currentServerLogName, ignoring $serverLogName');
                    }
                  }
                } catch (e) {
                  logService.error('DataManager', 'Failed to parse server info in status event', e);
                }
              }

              // 特别处理首次自动连接的情况
              if (status == ConnectionStatus.connected && connectedTime != null) {
                logService.info('DataManager', 'First connection successful, setting connection time: ${connectedTime.toString()}');
              }

              // Check if status event contains network interface information (iOS platform integration)
              if (status == ConnectionStatus.connected &&
                  eventData.containsKey('interface_name') &&
                  eventData.containsKey('local_ip') &&
                  (eventData.containsKey('itforce_ip') || eventData.containsKey('panabit_ip'))) {

                try {
                  // Create InterfaceInfo from status event data
                  // Handle both 'itforce_ip' (iOS) and 'panabit_ip' (legacy) field names
                  final tunIp = eventData['itforce_ip'] as String? ?? eventData['panabit_ip'] as String?;

                  final interfaceInfo = InterfaceInfo(
                    interfaceName: eventData['interface_name'] as String?,
                    localIp: eventData['local_ip'] as String?,
                    tunIp: tunIp,
                    timestamp: DateTime.now(),
                  );

                  logService.info('DataManager', 'Status event contains network interface info - '
                      'interface: ${interfaceInfo.interfaceName}, '
                      'local_ip: ${interfaceInfo.localIp}, '
                      'itforce_ip: ${interfaceInfo.tunIp ?? "empty"}');

                  // Update interface info directly from status event
                  // updateInterfaceInfo方法内部会处理cloud ip为空时的保护逻辑
                  appState.updateInterfaceInfo(interfaceInfo);

                  logService.info('DataManager', 'Network interface info updated from status event (iOS platform)');
                } catch (e) {
                  logService.error('DataManager', 'Failed to parse network interface info from status event: $e');
                }
              }

              // 处理特殊的message类型（重连成功、连接失败、重连失败）
              if (message.startsWith('reconnection_success')) {
                logService.info('DataManager', 'VPN reconnection successful - tunnel_ip: ${eventData['tunnel_ip']}, server: ${eventData['server_address']}');
              } else if (message.startsWith('connection_failure:')) {
                final errorMessage = message.substring('connection_failure:'.length).trim();
                logService.warning('DataManager', 'VPN connection failed - error: $errorMessage, server: ${eventData['server_address']}');

                // 显示连接失败通知（与Android/Windows保持一致）
                try {
                  // 使用ErrorHandler处理，它会自动进行国际化
                  ErrorHandler.handleApiError(
                    ApiException('VPN connection failed: $errorMessage', 1004, 'connection_failure'),
                    context: 'DataManager'
                  );
                } catch (e) {
                  logService.warning('DataManager', 'Failed to show connection failure notification: $e');
                }
              } else if (message.startsWith('reconnection_failure:')) {
                final errorMessage = message.substring('reconnection_failure:'.length).trim();
                logService.warning('DataManager', 'VPN reconnection failed - error: $errorMessage, server: ${eventData['server_address']}');

                // 显示重连失败通知（与Android/Windows保持一致）
                try {
                  // 使用ErrorHandler处理，它会自动进行国际化
                  ErrorHandler.handleApiError(
                    ApiException('VPN reconnection failed: $errorMessage', 1005, 'reconnection_failure'),
                    context: 'DataManager'
                  );
                } catch (e) {
                  logService.warning('DataManager', 'Failed to show reconnection failure notification: $e');
                }
              }

              appState.updateConnectionStatus(
                status,
                message: message,
                connectedTime: connectedTime,
              );

              // logService.debug('DataManager', 'Status event processing completed: $status, connection time: ${connectedTime?.toString() ?? 'null'}');
            } catch (e) {
              logService.error('DataManager', 'Failed to parse connection status', e);
            }
          }
          break;

        case 'servers':
          if (eventData != null && eventData is List) {
            try {
              final servers = (eventData)
                  .map((server) => Server.fromJson(server))
                  .toList();
              _handleServerListUpdate(servers);
            } catch (e) {
              logService.error('DataManager', 'Failed to parse server list: $e');
              // logService.debug('DataManager', 'Server data format: ${eventData.runtimeType}, content: $eventData');
            }
          } else {
            logService.warning('DataManager', 'Received invalid server list data: ${eventData?.runtimeType}');
          }
          break;
      }
    });

    // 监听服务器列表更新事件（智能处理列表变化）
    webSocketService.handleServerListEvent((servers) {
      // logService.debug('DataManager', 'Received server list update event, ${servers.length} servers');

      _servers = servers;

      // 处理当前选中服务器的情况
      if (appState.selectedServer != null) {
        final currentServerId = appState.selectedServer!.id;

        // 查找当前服务器是否还在新列表中
        final updatedCurrentServer = servers.firstWhere(
          (server) => server.id == currentServerId,
          orElse: () => Server.empty(),
        );

        if (updatedCurrentServer.id.isNotEmpty) {
          // 当前服务器仍在列表中，更新其信息
          // final serverLogName = updatedCurrentServer.nameEn.isNotEmpty ? updatedCurrentServer.nameEn : updatedCurrentServer.name;
          // logService.debug('DataManager', 'Updated current server info: $serverLogName');
          appState.selectServer(updatedCurrentServer, source: ServerSelectionSource.systemDefault);
        } else {
          // 当前服务器不在新列表中，清除选择，等待用户重新选择
          final currentServerLogName = appState.selectedServer!.nameEn.isNotEmpty ? appState.selectedServer!.nameEn : appState.selectedServer!.name;
          logService.warning('DataManager', 'Current server $currentServerLogName not in new list, clearing selection');
          // 不自动选择替代服务器，让用户手动选择
        }
      } else {
        // 没有选中服务器，不进行默认选择，等待登录时的best_server设置
        // logService.debug('DataManager', 'No selected server, waiting for login best_server selection');
      }

      // logService.debug('DataManager', 'Server list update processing completed');
    });

    // 监听Ping结果事件（直接更新服务器列表）
    // 注意：这个监听器是必要的，因为它处理ping_results事件，与主监听器处理的status事件不同
    webSocketService.handlePingResultsEvent((servers) {
      // logService.debug('DataManager', 'Received Ping results event, updating ${servers.length} servers');

      // 直接更新服务器列表缓存，UI会从这里获取最新的延迟数据
      _servers = servers;

      // 通知监听器服务器列表已更新（包含最新延迟信息）
      notifyListeners();

      // logService.debug('DataManager', 'Ping results processing completed, UI will get latest ping data directly from server list');
    });
  }

  /// _parseConnectionStatus
  ///
  /// DESCRIPTION:
  ///     解析连接状态字符串为枚举值
  ///
  /// PARAMETERS:
  ///     status - 状态值（可能为字符串或其他类型）
  ///
  /// RETURNS:
  ///     ConnectionStatus - 连接状态枚举值
  ConnectionStatus _parseConnectionStatus(dynamic status) {
    if (status == null) return ConnectionStatus.disconnected;

    final statusStr = status.toString().toLowerCase();
    switch (statusStr) {
      case 'connected':
        return ConnectionStatus.connected;
      case 'connecting':
        return ConnectionStatus.connecting;
      case 'disconnecting':
        return ConnectionStatus.disconnecting;
      case 'error':
        return ConnectionStatus.error;
      default:
        return ConnectionStatus.disconnected;
    }
  }

  /// _handleServerListUpdate
  ///
  /// DESCRIPTION:
  ///     处理服务器列表更新，如果没有选中服务器则选择默认服务器
  ///
  /// PARAMETERS:
  ///     servers - 新的服务器列表
  ///
  /// RETURNS:
  ///     void
  void _handleServerListUpdate(List<Server> servers) {
    _servers = servers;
    // 移除默认服务器选择逻辑，只保留登录时的best_server设置
    // logService.debug('DataManager', 'Server list updated, total ${servers.length} servers');
  }

  /// _updateServerInList
  ///
  /// DESCRIPTION:
  ///     更新服务器列表中特定服务器的信息
  ///
  /// PARAMETERS:
  ///     updatedServer - 更新后的服务器信息
  ///
  /// RETURNS:
  ///     void
  void _updateServerInList(Server updatedServer) {
    final index = _servers.indexWhere((server) => server.id == updatedServer.id);
    if (index != -1) {
      _servers[index] = updatedServer;
      // 通知监听器服务器列表已更新
      notifyListeners();
      // logService.debug('DataManager', 'Server updated in list - index: $index, server: ${updatedServer.name}, ping: ${updatedServer.ping}ms');
    } else {
      logService.warning('DataManager', 'Server not found in list for latency update - id: ${updatedServer.id}');
    }
  }



  /// initializeData
  ///
  /// DESCRIPTION:
  ///     初始化应用数据，包括WebSocket连接、服务器列表和用户信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 初始化过程中发生错误时抛出异常
  Future<void> initializeData() async {
    appState.updateLoadingStatus(true);

    try {
      // 确保WebSocket连接
      await _ensureWebSocketConnection();

      // 强制刷新服务器列表（登录时需要最新数据）
      // logService.debug('DataManager', 'Force refreshing server list during initialization');
      await _loadServers();

      // 加载用户信息
      await loadUserInfo();

      // 检查并重新加载路由设置（如果为空）
      await _checkAndReloadRoutingSettings();

      // 启动数据刷新定时器
      _startDataRefreshTimer();

      // logService.debug('DataManager', 'Data initialization completed');
    } catch (e) {
      ErrorHandler.handleGenericError(e, context: 'DataManager', userMessage: 'Data initialization failed');
    } finally {
      appState.updateLoadingStatus(false);
    }
  }

  /// _ensureWebSocketConnection
  ///
  /// DESCRIPTION:
  ///     确保WebSocket连接已建立，如果未连接则尝试连接
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _ensureWebSocketConnection() async {
    if (!webSocketService.isConnected) {
      logService.info('DataManager', 'WebSocket not connected detected, attempting to connect...');
      final connected = await webSocketService.connect();

      if (connected) {
        logService.info('DataManager', 'WebSocket connected successfully');
        appState.updateWebSocketStatus(true);
      } else {
        logService.warning('DataManager', 'WebSocket connection failed, will continue attempting reconnection in background');
        appState.updateWebSocketStatus(false);
      }
    } else {
      logService.info('DataManager', 'WebSocket already connected');
      appState.updateWebSocketStatus(true);
    }
  }

  /// _loadServers
  ///
  /// DESCRIPTION:
  ///     从API加载服务器列表并选择默认服务器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - API调用失败时抛出异常
  Future<void> _loadServers() async {
    try {
      _servers = await apiService.getServers();

      // 移除默认服务器选择逻辑，只保留登录时的best_server设置
      // logService.debug('DataManager', 'Server list loaded, total ${_servers.length} servers');

      // logService.debug('DataManager', 'Server list loading completed, total ${_servers.length} servers');
    } catch (e) {
      ErrorHandler.handleApiError(e, context: 'DataManager');
    }
  }

  /// loadUserInfo
  ///
  /// DESCRIPTION:
  ///     从本地存储加载用户信息并更新应用状态
  ///
  /// PARAMETERS:
  ///     username - 可选的用户名，用于加载特定用户的信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 加载过程中发生错误时抛出异常
  Future<void> loadUserInfo([String? username]) async {
    try {
      // 使用异步操作管理器执行，避免阻塞UI
      await AsyncOperationManager.executeNonBlocking<void>(
        operation: () async {
          // 如果提供了用户名，使用基于用户名的键
          String userInfoKey = StorageKeys.userInfo;
          if (username != null && username.isNotEmpty) {
            userInfoKey = '${StorageKeys.userInfo}_$username';
          }

          final userInfoJson = await _storageService.getString(userInfoKey);

          if (userInfoJson != null) {
            final userInfoData = Map<String, dynamic>.from(
              Map.castFrom(const JsonDecoder().convert(userInfoJson)),
            );

            final userInfo = UserInfo.fromJson(userInfoData);
            appState.updateUserInfo(userInfo);

            // logService.debug('DataManager', 'User info loaded successfully, username: $username');
          } else if (username != null && username.isNotEmpty) {
            // 如果没有找到用户特定的信息，创建默认信息
            final userInfo = UserInfo(
              displayName: username, // 默认显示名称为用户名
            );
            appState.updateUserInfo(userInfo);
            // logService.debug('DataManager', 'Created new user info, username: $username');
          }
        },
        operationId: 'load_user_info_${username ?? 'default'}',
        timeout: const Duration(seconds: 5),
      );
    } catch (e) {
      logService.error('DataManager', 'Failed to load user info', e);
    }
  }

  /// saveUserInfo
  ///
  /// DESCRIPTION:
  ///     保存用户信息到本地存储
  ///
  /// PARAMETERS:
  ///     userInfo - 要保存的用户信息对象
  ///     username - 可选的用户名，用于保存特定用户的信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 保存过程中发生错误时抛出异常
  Future<void> saveUserInfo(UserInfo userInfo, [String? username]) async {
    try {
      // 使用异步操作管理器执行，避免阻塞UI
      await AsyncOperationManager.executeNonBlocking<void>(
        operation: () async {
          final userInfoJson = json.encode(userInfo.toJson());

          // 使用基于用户名的键来保存用户信息
          String userInfoKey = StorageKeys.userInfo;
          if (username != null && username.isNotEmpty) {
            userInfoKey = '${StorageKeys.userInfo}_$username';
          }

          await _storageService.setString(userInfoKey, userInfoJson);

          appState.updateUserInfo(userInfo);
          logService.info('DataManager', 'User info saved successfully, username: $username');
        },
        operationId: 'save_user_info_${username ?? 'default'}',
        timeout: const Duration(seconds: 5),
      );
    } catch (e) {
      ErrorHandler.handleGenericError(e, context: 'DataManager');
    }
  }

  // 流量统计通过WebSocket实时推送，不再需要HTTP API轮询
  // refreshTrafficStats方法已移除，流量统计数据通过WebSocket事件处理器自动更新

  /// refreshInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     刷新网络接口信息，获取最新的接口状态和配置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 接口信息获取失败时抛出异常
  Future<void> refreshInterfaceInfo() async {
    try {
      // Debug: Log interface info refresh start
      // logService.debug('DataManager', 'Starting interface info refresh');

      // 使用非阻塞操作获取接口信息，避免UI阻塞
      final interfaceData = await AsyncOperationManager.executeNonBlocking<Map<String, dynamic>>(
        operation: () => apiService.getInterfaceInfo(),
        operationId: 'refresh_interface_info',
        timeout: const Duration(seconds: 10),
      );

      if (interfaceData != null) {
        // Debug: Log received interface data
        // logService.debug('DataManager', 'Received interface data: $interfaceData');

        final interfaceInfo = InterfaceInfo.fromJson(interfaceData);

        // Debug: Log parsed interface info
        logService.debug('DataManager', 'Parsed interface info from API - '
            'interfaceName: ${interfaceInfo.interfaceName}, '
            'localIp: ${interfaceInfo.localIp}, '
            'tunIp: ${interfaceInfo.tunIp ?? "empty"}');

        // updateInterfaceInfo方法内部会处理cloud ip为空时的保护逻辑
        appState.updateInterfaceInfo(interfaceInfo);

        // logService.debug('DataManager', 'Interface info updated in AppState');
      } else {
        // logService.debug('DataManager', 'Received null interface data');
      }
    } catch (e) {
      // Debug: Log interface info refresh error with details
      logService.error('DataManager', 'Interface info refresh failed: $e');
    }
  }

  /// servers getter
  ///
  /// DESCRIPTION:
  ///     获取不可修改的服务器列表副本
  ///
  /// RETURNS:
  ///     List<Server> - 服务器列表的不可修改副本
  List<Server> get servers => List.unmodifiable(_servers);

  /// refreshServers
  ///
  /// DESCRIPTION:
  ///     强制从后端获取最新的服务器列表
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> refreshServers() async {
    await _loadServers();
  }

  /// refreshServersOnLogin
  ///
  /// DESCRIPTION:
  ///     在用户登录时刷新服务器列表
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> refreshServersOnLogin() async {
    logService.info('DataManager', 'Refreshing server list on login');
    await _loadServers();
  }

  /// _startDataRefreshTimer
  ///
  /// DESCRIPTION:
  ///     启动数据刷新定时器，定期刷新接口信息和其他数据
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startDataRefreshTimer() {
    _stopDataRefreshTimer();
    _dataRefreshTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _refreshData();
    });
  }

  /// _stopDataRefreshTimer
  ///
  /// DESCRIPTION:
  ///     停止数据刷新定时器，释放定时器资源
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopDataRefreshTimer() {
    _dataRefreshTimer?.cancel();
    _dataRefreshTimer = null;
  }

  /// _refreshData
  ///
  /// DESCRIPTION:
  ///     定期刷新数据，包括接口信息等非实时数据
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _refreshData() async {
    try {
      // 接口信息通过WebSocket实时推送，不需要轮询API调用
      // 这样可以避免API调用返回null值与WebSocket推送的正确值之间的竞争
      // 流量统计也通过WebSocket实时推送，不需要轮询

      // 移除接口信息的API调用，只依赖WebSocket推送
      // if (appState.connectionStatus == ConnectionStatus.connected) {
      //   await refreshInterfaceInfo();
      // }

      // logService.debug('DataManager', 'Data refresh completed (interface info via WebSocket only)');
    } catch (e) {
      // 静默处理刷新错误，避免频繁显示错误信息
      logService.warning('DataManager', 'Data refresh failed');
    }
  }

  /// clearUserData
  ///
  /// DESCRIPTION:
  ///     清除用户相关数据，包括用户信息和认证数据
  ///
  /// PARAMETERS:
  ///     username - 可选的用户名，用于清除特定用户的数据
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 清除过程中发生错误时抛出异常
  Future<void> clearUserData([String? username]) async {
    try {
      // 使用异步操作管理器执行，避免阻塞UI
      await AsyncOperationManager.executeNonBlocking<void>(
        operation: () async {
          if (username != null && username.isNotEmpty) {
            // 清除特定用户的信息
            final userInfoKey = '${StorageKeys.userInfo}_$username';
            await _storageService.remove(userInfoKey);
            logService.info('DataManager', 'Cleared user data: $username');
          } else {
            // 清除当前用户信息（通用键）
            await _storageService.remove(StorageKeys.userInfo);

            // 注意：由于跨平台存储服务接口不提供getKeys方法，
            // 我们无法批量清除所有用户特定的数据。
            // 这是一个设计权衡，以保持接口简洁。
            // 如果需要清除特定用户数据，请使用带用户名参数的调用。
            logService.info('DataManager', 'Cleared default user data');
          }

          // 重置应用状态中的用户信息
          appState.updateUserInfo(UserInfo());

          logService.info('DataManager', 'User data clearing completed');
        },
        operationId: 'clear_user_data_${username ?? 'all'}',
        timeout: const Duration(seconds: 5),
      );
    } catch (e) {
      logService.error('DataManager', 'Failed to clear user data', e);
    }
  }

  /// clearAllSensitiveData
  ///
  /// DESCRIPTION:
  ///     清除所有敏感数据（包括密码），在注销时调用以确保完全清除敏感信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 清除过程中发生错误时抛出异常
  Future<void> clearAllSensitiveData() async {
    try {
      await AsyncOperationManager.executeNonBlocking<void>(
        operation: () async {
          // 清除所有认证相关的敏感数据
          await _storageService.removeSecureString(StorageKeys.password);
          await _storageService.remove(StorageKeys.rememberCredentials);

          // 清除所有用户信息
          await _storageService.remove(StorageKeys.userInfo);

          // 注意：由于跨平台存储服务接口不提供getKeys方法，
          // 我们无法批量清除所有用户特定的数据。
          // 敏感数据（如密码）已通过安全存储清除。

          // 重置应用状态
          appState.updateUserInfo(UserInfo());

          logService.info('DataManager', 'All sensitive data cleared');
        },
        operationId: 'clear_all_sensitive_data',
        timeout: const Duration(seconds: 5),
      );
    } catch (e) {
      logService.error('DataManager', 'Failed to clear sensitive data', e);
    }
  }

  /// reset
  ///
  /// DESCRIPTION:
  ///     重置数据管理器状态，清除数据并停止定时器，但不释放实例
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void reset() {
    // 停止定时器
    _stopDataRefreshTimer();

    // 清除服务器列表
    _servers.clear();

    // 通知监听器状态已重置
    notifyListeners();

    logService.info('DataManager', 'DataManager reset completed');
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理数据管理器资源，停止定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  @override
  void dispose() {
    _stopDataRefreshTimer();
    super.dispose();
  }

  // 移除了_selectBestServer和_getSortedServersByLatency方法
  // 现在只使用登录时的best_server作为默认服务器选择

  /// _checkAndReloadRoutingSettings
  ///
  /// DESCRIPTION:
  ///     检查路由设置是否为空，如果为空则重新加载
  ///     这解决了登录后路由设置界面显示空白的问题
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _checkAndReloadRoutingSettings() async {
    try {
      final routingSettingsService = serviceLocator<RoutingSettingsService>();
      final routingSettingsModel = serviceLocator<RoutingSettingsModel>();

      // 检查当前路由设置是否为空
      if (routingSettingsModel.customRoutes.isEmpty &&
          routingSettingsModel.mode == RoutingMode.all) {

        final logSvc = _helper.getLogService();
        logSvc.info('DataManager', 'Routing settings appear to be empty, attempting to reload from storage');

        // 从本地存储重新加载路由设置
        final savedSettings = await routingSettingsService.loadRoutingSettings();
        if (savedSettings != null) {
          // 更新模型
          routingSettingsModel.updateSettings(
            mode: savedSettings.mode,
            customRoutes: savedSettings.customRoutes,
            autoStart: savedSettings.autoStart,
          );

          logSvc.info('DataManager',
            'Routing settings reloaded successfully: mode=${savedSettings.mode}, customRoutes=${savedSettings.customRoutes}');
        } else {
          logSvc.info('DataManager', 'No saved routing settings found in storage');
        }
      } else {
        final logSvc = _helper.getLogService();
        logSvc.info('DataManager',
          'Routing settings already loaded: mode=${routingSettingsModel.mode}, customRoutes=${routingSettingsModel.customRoutes}');
      }
    } catch (e) {
      final logSvc = _helper.getLogService();
      logSvc.error('DataManager', 'Failed to check and reload routing settings', e);
      // 不抛出异常，避免影响其他初始化流程
    }
  }
}

/// _DataManagerHelper
///
/// PURPOSE:
///     DataManager的辅助类，提供统一的工具方法和错误处理
///
/// FEATURES:
///     - 统一的服务获取方法
///     - 统一的错误处理模式
///     - 统一的日志记录格式
///     - 异步操作的安全执行
///
/// USAGE:
///     由DataManager内部使用，提供公共功能支持
class _DataManagerHelper {
  /// getAppState
  ///
  /// DESCRIPTION:
  ///     安全获取AppState服务实例
  ///
  /// RETURNS:
  ///     AppState - AppState服务实例
  ///
  /// THROWS:
  ///     Exception - 服务获取失败时抛出异常
  AppState getAppState() {
    try {
      return serviceLocator<AppState>();
    } catch (e) {
      throw Exception('Failed to get AppState service: $e');
    }
  }

  /// getApiService
  ///
  /// DESCRIPTION:
  ///     安全获取ApiService服务实例
  ///
  /// RETURNS:
  ///     ApiService - API服务实例
  ///
  /// THROWS:
  ///     Exception - 服务获取失败时抛出异常
  ApiService getApiService() {
    try {
      return serviceLocator<ApiService>();
    } catch (e) {
      throw Exception('Failed to get ApiService: $e');
    }
  }

  /// getLogService
  ///
  /// DESCRIPTION:
  ///     安全获取LogService服务实例
  ///
  /// RETURNS:
  ///     LogService - 日志服务实例
  ///
  /// THROWS:
  ///     Exception - 服务获取失败时抛出异常
  LogService getLogService() {
    try {
      return serviceLocator<LogService>();
    } catch (e) {
      throw Exception('Failed to get LogService: $e');
    }
  }

  /// getWebSocketService
  ///
  /// DESCRIPTION:
  ///     安全获取WebSocketService服务实例
  ///
  /// RETURNS:
  ///     WebSocketService - WebSocket服务实例
  ///
  /// THROWS:
  ///     Exception - 服务获取失败时抛出异常
  WebSocketService getWebSocketService() {
    try {
      return serviceLocator<WebSocketService>();
    } catch (e) {
      throw Exception('Failed to get WebSocketService: $e');
    }
  }

  /// handleAsyncError
  ///
  /// DESCRIPTION:
  ///     统一处理异步操作中的错误
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///     context - 错误上下文
  ///     userMessage - 用户友好的错误消息
  ///
  /// RETURNS:
  ///     void
  void handleAsyncError(dynamic error, String context, [String? userMessage]) {
    try {
      final logService = getLogService();
      logService.error('DataManager', 'Error in $context: ${error.toString()}');

      if (userMessage != null) {
        ErrorHandler.handleGenericError(error, context: context, userMessage: userMessage);
      }
    } catch (e) {
      // 如果连日志服务都获取不到，静默处理避免在生产环境中输出调试信息
    }
  }

  /// logDebug
  ///
  /// DESCRIPTION:
  ///     安全的调试日志记录
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  void logDebug(String message) {
    try {
      // final logService = getLogService();
      // logService.debug('DataManager', message);
    } catch (e) {
      // 静默处理，避免在生产环境中输出调试信息
    }
  }

  /// logInfo
  ///
  /// DESCRIPTION:
  ///     安全的信息日志记录
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  void logInfo(String message) {
    try {
      final logService = getLogService();
      logService.info('DataManager', message);
    } catch (e) {
      // 静默处理，避免在生产环境中输出调试信息
    }
  }

  /// logWarning
  ///
  /// DESCRIPTION:
  ///     安全的警告日志记录
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  void logWarning(String message) {
    try {
      final logService = getLogService();
      logService.warning('DataManager', message);
    } catch (e) {
      // 静默处理，避免在生产环境中输出调试信息
    }
  }

  /// logError
  ///
  /// DESCRIPTION:
  ///     安全的错误日志记录
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///     error - 可选的错误对象
  ///
  /// RETURNS:
  ///     void
  void logError(String message, [dynamic error]) {
    try {
      final logService = getLogService();
      logService.error('DataManager', message, error);
    } catch (e) {
      // 静默处理，避免在生产环境中输出调试信息
    }
  }

}
